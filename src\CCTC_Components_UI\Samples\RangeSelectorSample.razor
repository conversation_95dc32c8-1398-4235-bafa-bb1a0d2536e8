@page "/rangeselectorsample"

@using Microsoft.AspNetCore.Components

@{
    var description = new List<string>
    {
        "The RangeSelector component provides an interactive range selection control that allows users to select a range of values within defined bounds. " +
        "It supports multiple data types including integers, doubles, DateTime, and TimeSpan with customizable formatting and conversion functions."
    };

    var features = new List<(string, string)>
    {
        ("multiple data types", "supports <code>int</code>, <code>double</code>, <code>DateTime</code>, and <code>TimeSpan</code> with generic type parameter"),
        ("custom formatting", "use <code>DisplayFormatter</code> to control how values are displayed to users"),
        ("conversion functions", "provide <code>ValueFromPercentage</code> and <code>PercentageFromValue</code> for complex data types"),
        ("step control", "use <code>StepFunction</code> to define custom step sizes for keyboard navigation"),
        ("interactive handles", "drag handles or use arrow keys for precise control"),
    };

    var gotchas = new List<(string, string)>
    {
        ("type constraints", "the generic type must implement <code>IComparable&lt;T&gt;</code>"),
        ("conversion functions", "required for non-numeric types like DateTime and TimeSpan"),
        ("step function", "should return appropriate step sizes based on the range magnitude")
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Integer range", "A simple integer range selector with default behavior",
            @"<RangeSelector TValue=""int""
    Id=""integer-range""
    InitialInnerRangeLower=""10""
    InitialInnerRangeUpper=""90""
    InitialOuterRangeLower=""0""
    InitialOuterRangeUpper=""100""
    ActualInnerRangeLower=""@OnIntegerLowerChanged""
    ActualInnerRangeUpper=""@OnIntegerUpperChanged"">
    <p>Current Range: @currentIntegerLower - @currentIntegerUpper</p>
</RangeSelector>",
            @<RangeSelector TValue="int"
                Id="integer-range"
                InitialInnerRangeLower="@currentIntegerLower"
                InitialInnerRangeUpper="@currentIntegerUpper"
                InitialOuterRangeLower="0"
                InitialOuterRangeUpper="100"
                ActualInnerRangeLower="@OnIntegerLowerChanged"
                ActualInnerRangeUpper="@OnIntegerUpperChanged">
                <p>Current Range: @currentIntegerLower - @currentIntegerUpper</p>
            </RangeSelector>
        ),

        ("Double range with formatting", "A double range with custom display formatting",
            @"<RangeSelector TValue=""double""
    Id=""double-range""
    InitialInnerRangeLower=""2.5""
    InitialInnerRangeUpper=""8.7""
    InitialOuterRangeLower=""0.0""
    InitialOuterRangeUpper=""10.0""
    ActualInnerRangeLower=""@OnDoubleLowerChanged""
    ActualInnerRangeUpper=""@OnDoubleUpperChanged""
    DisplayFormatter=""@(d => d.ToString(""F2""))"">
    <p>Current Range: @currentDoubleLower.ToString(""F2"") - @currentDoubleUpper.ToString(""F2"")</p>
</RangeSelector>",
            @<RangeSelector TValue="double"
                Id="double-range"
                InitialInnerRangeLower="@currentDoubleLower"
                InitialInnerRangeUpper="@currentDoubleUpper"
                InitialOuterRangeLower="0.0"
                InitialOuterRangeUpper="10.0"
                ActualInnerRangeLower="@OnDoubleLowerChanged"
                ActualInnerRangeUpper="@OnDoubleUpperChanged"
                DisplayFormatter="@(d => d.ToString("F2"))">
                <p>Current Range: @currentDoubleLower.ToString("F2") - @currentDoubleUpper.ToString("F2")</p>
            </RangeSelector>
        ),
            <!-- Integer Range Example -->
            <h4>Integer Range (0-100)</h4>
            <RangeSelector TValue="int"
                Id="cctcRangeSelector1"
                InitialInnerRangeLower="@currentIntegerLower"
                InitialInnerRangeUpper="@currentIntegerUpper"
                InitialOuterRangeLower="0"
                InitialOuterRangeUpper="100"
                ActualInnerRangeLower="@OnIntegerLowerChanged"
                ActualInnerRangeUpper="@OnIntegerUpperChanged">
                <p>Drag the inner range handles to change the values.</p>
                <p>Current Outer Range: 0 - 100</p>
                <p>Current Inner Range: @currentIntegerLower - @currentIntegerUpper</p>
                <p>Use arrow keys when focused on handles for precise control.</p>
            </RangeSelector>

            <hr style="margin: 30px 0;" />

            <!-- Double Range Example -->
            <h4>Double Range (0.0-10.0)</h4>
            <RangeSelector TValue="double"
                Id="cctcRangeSelector2"
                InitialInnerRangeLower="@currentDoubleLower"
                InitialInnerRangeUpper="@currentDoubleUpper"
                InitialOuterRangeLower="0.0"
                InitialOuterRangeUpper="10.0"
                ActualInnerRangeLower="@OnDoubleLowerChanged"
                ActualInnerRangeUpper="@OnDoubleUpperChanged"
                DisplayFormatter="@(d => d.ToString("F2"))">
                <p>Drag the inner range handles to change the values.</p>
                <p>Current Outer Range: 0.00 - 10.00</p>
                <p>Current Inner Range: @currentDoubleLower.ToString("F2") - @currentDoubleUpper.ToString("F2")</p>
                <p>Use arrow keys when focused on handles for precise control.</p>
            </RangeSelector>

            <hr style="margin: 30px 0;" />

            <!-- DateTime Range Example -->
            <h4>DateTime Range (Last 30 Days)</h4>
            <RangeSelector TValue="DateTime"
                Id="cctcRangeSelector3"
                InitialInnerRangeLower="@currentDateTimeLower"
                InitialInnerRangeUpper="@currentDateTimeUpper"
                InitialOuterRangeLower="@dateTimeMin"
                InitialOuterRangeUpper="@dateTimeMax"
                ActualInnerRangeLower="@OnDateTimeLowerChanged"
                ActualInnerRangeUpper="@OnDateTimeUpperChanged"
                ValueFromPercentage="@ConvertPercentageToDateTime"
                PercentageFromValue="@ConvertDateTimeToPercentage"
                StepFunction="@GetDateTimeStepSize"
                DisplayFormatter="@(dt => dt.ToString("MMM dd"))">
                <p>Drag the inner range handles to change the dates.</p>
                <p>Current Outer Range: @dateTimeMin.ToString("MMM dd") - @dateTimeMax.ToString("MMM dd")</p>
                <p>Current Inner Range: @currentDateTimeLower.ToString("MMM dd") - @currentDateTimeUpper.ToString("MMM dd")</p>
                <p>Use arrow keys when focused on handles for precise control.</p>
            </RangeSelector>

            <hr style="margin: 30px 0;" />

            <!-- TimeSpan Range Example -->
            <h4>TimeSpan Range (0-24 Hours)</h4>
            <RangeSelector TValue="TimeSpan"
                Id="cctcRangeSelector4"
                InitialInnerRangeLower="@currentTimeSpanLower"
                InitialInnerRangeUpper="@currentTimeSpanUpper"
                InitialOuterRangeLower="@timeSpanMin"
                InitialOuterRangeUpper="@timeSpanMax"
                ActualInnerRangeLower="@OnTimeSpanLowerChanged"
                ActualInnerRangeUpper="@OnTimeSpanUpperChanged"
                ValueFromPercentage="@ConvertPercentageToTimeSpan"
                PercentageFromValue="@ConvertTimeSpanToPercentage"
                StepFunction="@GetTimeSpanStepSize"
                DisplayFormatter="@(ts => $"{ts.Hours:D2}:{ts.Minutes:D2}")">
                <p>Drag the inner range handles to change the time range.</p>
                <p>Current Outer Range: @timeSpanMin.ToString(@"hh\:mm") - @timeSpanMax.ToString(@"hh\:mm")</p>
                <p>Current Inner Range: @currentTimeSpanLower.ToString(@"hh\:mm") - @currentTimeSpanUpper.ToString(@"hh\:mm")</p>
                <p>Use arrow keys when focused on handles for precise control.</p>
            </RangeSelector>
        </ExampleTemplate>
    </Sampler>
</div>

@code {
    // Integer range
    int currentIntegerLower = 10;
    int currentIntegerUpper = 90;

    // Double range
    double currentDoubleLower = 2.5;
    double currentDoubleUpper = 8.7;

    // DateTime range (last 30 days)
    DateTime dateTimeMin = DateTime.Today.AddDays(-30);
    DateTime dateTimeMax = DateTime.Today;
    DateTime currentDateTimeLower;
    DateTime currentDateTimeUpper;

    // TimeSpan range (24 hours)
    TimeSpan timeSpanMin = TimeSpan.Zero;
    TimeSpan timeSpanMax = TimeSpan.FromHours(24);
    TimeSpan currentTimeSpanLower = TimeSpan.FromHours(9);
    TimeSpan currentTimeSpanUpper = TimeSpan.FromHours(17);

    protected override void OnInitialized()
    {
        currentDateTimeLower = dateTimeMin.AddDays(5);
        currentDateTimeUpper = dateTimeMax.AddDays(-3);
    }

    // Integer event handlers
    private void OnIntegerLowerChanged(int newValue)
    {
        currentIntegerLower = newValue;
        StateHasChanged();
    }

    private void OnIntegerUpperChanged(int newValue)
    {
        currentIntegerUpper = newValue;
        StateHasChanged();
    }

    // Double event handlers
    private void OnDoubleLowerChanged(double newValue)
    {
        currentDoubleLower = newValue;
        StateHasChanged();
    }

    private void OnDoubleUpperChanged(double newValue)
    {
        currentDoubleUpper = newValue;
        StateHasChanged();
    }

    // DateTime event handlers
    private void OnDateTimeLowerChanged(DateTime newValue)
    {
        currentDateTimeLower = newValue;
        StateHasChanged();
    }

    private void OnDateTimeUpperChanged(DateTime newValue)
    {
        currentDateTimeUpper = newValue;
        StateHasChanged();
    }

    // TimeSpan event handlers
    private void OnTimeSpanLowerChanged(TimeSpan newValue)
    {
        currentTimeSpanLower = newValue;
        StateHasChanged();
    }

    private void OnTimeSpanUpperChanged(TimeSpan newValue)
    {
        currentTimeSpanUpper = newValue;
        StateHasChanged();
    }

    // DateTime conversion functions
    private DateTime ConvertPercentageToDateTime(DateTime min, DateTime max, double percentage)
    {
        var totalTicks = (max - min).Ticks;
        var targetTicks = (long)(totalTicks * (percentage / 100.0));
        return min.AddTicks(targetTicks);
    }

    private double ConvertDateTimeToPercentage(DateTime min, DateTime max, DateTime value)
    {
        if (max == min) return 0;
        var totalTicks = (max - min).Ticks;
        var valueTicks = (value - min).Ticks;
        return (double)valueTicks / totalTicks * 100.0;
    }

    private int GetDateTimeStepSize(DateTime min, DateTime max)
    {
        var totalDays = (max - min).TotalDays;
        return totalDays > 30 ? 2 : 1; // Larger step for longer ranges
    }

    // TimeSpan conversion functions
    private TimeSpan ConvertPercentageToTimeSpan(TimeSpan min, TimeSpan max, double percentage)
    {
        var totalTicks = (max - min).Ticks;
        var targetTicks = (long)(totalTicks * (percentage / 100.0));
        return min.Add(TimeSpan.FromTicks(targetTicks));
    }

    private double ConvertTimeSpanToPercentage(TimeSpan min, TimeSpan max, TimeSpan value)
    {
        if (max == min) return 0;
        var totalTicks = (max - min).Ticks;
        var valueTicks = (value - min).Ticks;
        return (double)valueTicks / totalTicks * 100.0;
    }

    private int GetTimeSpanStepSize(TimeSpan min, TimeSpan max)
    {
        var totalHours = (max - min).TotalHours;
        return totalHours > 12 ? 2 : 1; // 30-minute steps for ranges over 12 hours
    }
}