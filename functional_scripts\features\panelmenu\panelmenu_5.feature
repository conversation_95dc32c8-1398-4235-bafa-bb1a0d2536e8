@component @panelmenu @panelmenu_5
Feature: one or multiple headers can be expanded in the panel menu
    Scenario: multiple headers are expanded in the panel menu
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the panel menu matches the base image {string}
        

        #this is the one with awkward usage i cannot make yet 