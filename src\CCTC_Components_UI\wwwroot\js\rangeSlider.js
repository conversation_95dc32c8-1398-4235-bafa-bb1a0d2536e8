let isDragging = false;
let dragThumbType = null;
let trackElement = null;
let dotNetReference = null;

export function initialize(track, dotNetRef) {
    trackElement = track;
    dotNetReference = dotNetRef;
    
    // Add document-level event listeners for better drag tolerance
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
    
    const thumbs = trackElement.querySelectorAll('.CCTC-range-selector-thumb');
    
    thumbs.forEach((thumb) => {
        const isLowerThumb = thumb.classList.contains('CCTC-range-selector-thumb-lower');
        
        // Remove draggable attribute - we'll handle this with mouse/touch events
        thumb.removeAttribute('draggable');
        
        thumb.addEventListener('mousedown', (e) => {
            e.preventDefault();
            startDrag(isLowerThumb);
        });
        
        thumb.addEventListener('touchstart', (e) => {
            e.preventDefault();
            startDrag(isLowerThumb);
        });
    });
    
    // Click on track to move nearest thumb
    trackElement.addEventListener('click', handleTrackClick);
}

export function startDrag(isLowerThumb) {
    isDragging = true;
    dragThumbType = isLowerThumb ? 'lower' : 'upper';
    
    const thumb = trackElement.querySelector(
        isLowerThumb ? '.CCTC-range-selector-thumb-lower' : '.CCTC-range-selector-thumb-upper'
    );
    thumb.classList.add('CCTC-dragging');
    
    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
}

function handleMouseMove(e) {
    if (!isDragging) return;
    
    e.preventDefault();
    updateThumbPosition(e.clientX);
}

function handleTouchMove(e) {
    if (!isDragging) return;
    
    e.preventDefault();
    if (e.touches.length > 0) {
        updateThumbPosition(e.touches[0].clientX);
    }
}

function handleMouseUp(e) {
    if (!isDragging) return;
    
    stopDrag();
}

function handleTouchEnd(e) {
    if (!isDragging) return;
    
    stopDrag();
}

function handleTrackClick(e) {
    if (isDragging) return;
    
    const rect = trackElement.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (clickX / rect.width) * 100));
    
    // Determine which thumb is closer to the click position
    const lowerThumb = trackElement.querySelector('.CCTC-range-selector-thumb-lower');
    const upperThumb = trackElement.querySelector('.CCTC-range-selector-thumb-upper');
    
    const lowerPos = parseFloat(lowerThumb.style.left) || 0;
    const upperPos = parseFloat(upperThumb.style.left) || 100;
    
    const distanceToLower = Math.abs(percentage - lowerPos);
    const distanceToUpper = Math.abs(percentage - upperPos);
    
    const isLowerThumb = distanceToLower < distanceToUpper;
    
    dotNetReference.invokeMethodAsync('UpdateValueAsync', percentage, isLowerThumb);
}

function updateThumbPosition(clientX) {
    const rect = trackElement.getBoundingClientRect();
    
    // Extended tolerance area - 50px above and below the track
    const tolerance = 50;
    
    // Calculate position relative to track
    const x = clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    
    const isLowerThumb = dragThumbType === 'lower';
    dotNetReference.invokeMethodAsync('UpdateValueAsync', percentage, isLowerThumb);
}

function stopDrag() {
    if (!isDragging) return;
    
    isDragging = false;
    
    const thumb = trackElement.querySelector(
        dragThumbType === 'lower' ? '.CCTC-range-selector-thumb-lower' : '.CCTC-range-selector-thumb-upper'
    );
    thumb.classList.remove('CCTC-dragging');
    
    // Restore text selection
    document.body.style.userSelect = '';
    
    dragThumbType = null;
    dotNetReference.invokeMethodAsync('StopDragAsync');
}

export function preventDefault(event) {
    event.preventDefault();
}

// Cleanup function for when the component is disposed
export function dispose() {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
}