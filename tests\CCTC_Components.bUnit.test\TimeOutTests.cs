﻿using CCTC_Components.Components;

namespace CCTC_Components.bUnit.test;

public class TimeOutTests : TestContext
{
    [Fact]
    public void SpaceIsReserved()
    {
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public void SpaceIsNotReserved()
    {
        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsNotReservedAndCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
            .Add(p => p.CollapseOnFadeComplete, true)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsNotReservedAndDoesNotCollapseAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, false)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }

    [Fact]
    public async Task SpaceIsReservedAndCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, true)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "no-reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "reserve");
    }

    [Fact]
    public async Task SpaceIsReservedDoesNotCollapsesAtCompletion()
    {
        var hasCompleted = false;

        var cut = RenderComponent<TimeOut>(parameters => parameters
            .Add(p => p.ChildContent, $"<div id='content-id'>Some text</div>")
            .Add(p => p.ReserveSpace, true)
            .Add(p => p.CollapseOnFadeComplete, false)
            .Add(p => p.ShowContentFor, TimeSpan.FromMilliseconds(1))
            .Add(p => p.FadeFor, TimeSpan.FromMilliseconds(1))

            .Add(p => p.AfterFadeFinished, () => hasCompleted = true)
        );

        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");

        cut.Instance.Initiate();

        await Task.Delay(TimeSpan.FromMilliseconds(100));

        //check has completed and that the ui is in the correct state
        Assert.True(hasCompleted);
        TestHelpers.AssertHasClass(cut.Find("cctc-timeout > div"), "reserve");
        TestHelpers.AssertDoesNotHaveClass(cut.Find("cctc-timeout > div"), "no-reserve");
    }
}