import { ICustomWorld } from '../support/custom-world';
import { compareToBaseImage } from '../utils/compareImages';
import * as Helpers from '../support/helper-functions';
import { When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When(
  'the user selects the {string} Panel menu header',
  async function (this: ICustomWorld, menuHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
  }
);

When(
  'the user selects the {string} Panel menu item',
  async function (this: ICustomWorld, menuItem: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

Then(
  'there is a panel menu item called {string}',
  async function (this: ICustomWorld, menuItem: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

Then(
  'there is a panel menu header called {string}',
  async function (this: ICustomWorld, menuHeader: string) {
    const header = Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header-title', { hasText: menuHeader });
    await expect(header.first()).toBeVisible();
  }
);

When(
  'the user selects the {string} Panel menu item in the {string} header',
  async function (this: ICustomWorld, menuItem: string, menuHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

When(
  'the user selects the {string} Panel menu item in the {string} header with the {string} subheader',
  async function (this: ICustomWorld, menuItem: string, menuHeader: string, subHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-subheader').getByText(subHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
  }
);

When(
  'the user collapses the Panel menu',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

// pretty sure this locator is wrong

When(
  'the user expands the Panel menu',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

// pretty sure this locator is wrong

When(
  'the user clicks the Panel menu collapse/expand button',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

// pretty sure this locator is wrong

When(
  'the user clicks the Panel menu collapse/expand button with the {string} header',
  async function (this: ICustomWorld, menuHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

// pretty sure this locator is wrong

When(
  'the user clicks the Panel menu collapse/expand button with the {string} header and {string} subheader',
  async function (this: ICustomWorld, menuHeader: string, subHeader: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-subheader').getByText(subHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

// pretty sure this locator is wrong

When(
  'the user clicks the Panel menu collapse/expand button with the {string} header and {string} subheader and {string} item',
  async function (this: ICustomWorld, menuHeader: string, subHeader: string, menuItem: string) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header').getByText(menuHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-subheader').getByText(subHeader).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true }).click();
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse-expand-button').click();
  }
);

When(
  'the user clicks the panel menu expand collapse bar',
  async function (this: ICustomWorld) {
    await Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-collapse').click();
  }
);

Then(
  'the panel menu item {int} has a tooltip enabled containing the full label text',
  async function (this: ICustomWorld, itemNumber: number) {
    // Find the panel menu item by its ID
    const panelMenuItem = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-panelmenuitem[id="MenuItem${itemNumber}"]`);
    // Find the tooltip associated with this menu item
    const tooltip = panelMenuItem.locator('cctc-tooltip');
    // Check if the tooltip exists
    await expect(tooltip).toHaveCount(1, { timeout: 5000 });
  }
);

Then(
  'the panel menu item {int} has no tooltip enabled',
  async function (this: ICustomWorld, itemNumber: number) {
    // Find the panel menu item by its ID
    const panelMenuItem = Helpers.getSamplerTabsSelectedContent(this)
      .locator(`cctc-panelmenuitem[id="MenuItem${itemNumber}"]`);
    // Find the tooltip associated with this menu item and check that it doesn't exist
    await expect(panelMenuItem.locator('cctc-tooltip')).toHaveCount(0);
  }
);

// above doenst work and i dont know why 

Then(
  'the panel menu matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenu')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the Panel menu header matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu item matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    const screenshot = await Helpers.tryGetStableScreenshot(
      Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem')
    );
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu item {string} is selected',
  async function (this: ICustomWorld, menuItem: string) {
    const item = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true });
    await expect(item.locator('.panelmenu-item')).toHaveClass(/panelmenu-item-in-scope/);
  }
);

Then(
  'the panel menu item {string} is not selected',
  async function (this: ICustomWorld, menuItem: string) {
    const item = Helpers.getSamplerTabsSelectedContent(this).locator('cctc-panelmenuitem').getByText(menuItem, { exact: true });
    await expect(item.locator('.panelmenu-item')).toHaveClass(/panelmenu-item-not-in-scope/);
  }
);

Then(
  'the panel menu shows {string} as selected',
  async function (this: ICustomWorld, menuItem: string) {
    const selectedText = Helpers.getSamplerTabsSelectedContent(this).getByText(`selected: ${menuItem}`, { exact: true });
    await expect(selectedText).toBeVisible();
  }
);

Then(
  'the selected panel menu text matches the base image {string}',
  async function (this: ICustomWorld, name: string) {
    // Wait for the element to be visible
    const selectedTextElement = this.page!.getByText(/selected:/, { exact: false });
    await expect(selectedTextElement).toBeVisible({ timeout: 5000 });
    // Take screenshot after ensuring visibility
    const screenshot = await Helpers.tryGetStableScreenshot(selectedTextElement);
    await compareToBaseImage(this, name, screenshot);
  }
);

Then(
  'the panel menu header {string} is {string}',
  async function (this: ICustomWorld, menuHeader: string, state: string) {
    const header = Helpers.getSamplerTabsSelectedContent(this).locator('.panelmenu-header', { hasText: menuHeader });
    const icon = header.locator('.panelmenu-header-expandcollapse-icon');
    if (state.toLowerCase() === 'open') {
      await expect(icon).toHaveText('expand_less');
    } else if (state.toLowerCase() === 'closed') {
      await expect(icon).toHaveText('expand_more');
    } else {
      throw new Error(`Invalid state: ${state}. Expected 'open' or 'closed'.`);
    }
  }
);

Then(
  'the panel menu expand and collapse arrows point {string}',
  async function (this: ICustomWorld, direction: string) {
    const collapseIcon = Helpers.getSamplerTabsSelectedContent(this)
      .locator('.panelmenu-collapse .material-icons');
    if (direction.toLowerCase() === 'left') {
      await expect(collapseIcon).toHaveText('keyboard_double_arrow_left');
    } else if (direction.toLowerCase() === 'right') {
      await expect(collapseIcon).toHaveText('keyboard_double_arrow_right');
    } else {
      throw new Error(`Invalid direction: ${direction}. Expected 'left' or 'right'.`);
    }
  }
);

Then(
  'the panel menu header {string} shows only the icon and not the text',
  async function (this: ICustomWorld, itemTitle: string) {
    // Find the panel menu item by ID - "menuitem without header" corresponds to MenuItem1
    // Based on the sample code, this is the first menu item
    const panelMenuItem = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-panelmenuitem[id="MenuItem1"]');

    // Check that the item icon is visible
    const itemIcon = panelMenuItem.locator('.material-icons');
    await expect(itemIcon).toBeVisible();

    // Check that the item title is not visible (should be hidden in collapsed/icons-only mode)
    // In collapsed mode, the title should not be visible in the main display
    const itemTitleElement = panelMenuItem.locator('.panelmenu-item-title');

    // In icons-only mode, the title element should not be present in the DOM
    await expect(itemTitleElement).toHaveCount(0);

    // Verify that tooltip is present (in collapsed mode, items show tooltips)
    const tooltip = panelMenuItem.locator('cctc-tooltip');
    await expect(tooltip).toHaveCount(1);

    // Verify the tooltip has the correct title in the data attribute
    await expect(tooltip).toHaveAttribute('data-bs-original-title', itemTitle);
  }
);

Then(
  'the panel menu item {string} shows only the icon and not the text',
  async function (this: ICustomWorld, itemTitle: string) {
    // Find the panel menu item that has a tooltip with the specified title
    // In icons-only mode, panel menu items show tooltips with their title text
    const panelMenuItems = Helpers.getSamplerTabsSelectedContent(this)
      .locator('cctc-panelmenuitem');

    // Find the specific item by checking for tooltip with matching title
    let targetMenuItem = null;
    const itemCount = await panelMenuItems.count();

    for (let i = 0; i < itemCount; i++) {
      const item = panelMenuItems.nth(i);
      const tooltip = item.locator('cctc-tooltip');

      if (await tooltip.count() > 0) {
        const tooltipTitle = await tooltip.getAttribute('data-bs-original-title');
        if (tooltipTitle === itemTitle) {
          targetMenuItem = item;
          break;
        }
      }
    }

    if (!targetMenuItem) {
      throw new Error(`Could not find panel menu item with title "${itemTitle}"`);
    }

    // Check that the item icon is visible
    const itemIcon = targetMenuItem.locator('.material-icons');
    await expect(itemIcon).toBeVisible();

    // Check that the item title is not visible (should be hidden in collapsed/icons-only mode)
    // In collapsed mode, the title should not be visible in the main display
    const itemTitleElement = targetMenuItem.locator('.panelmenu-item-title');

    // In icons-only mode, the title element should not be present in the DOM
    await expect(itemTitleElement).toHaveCount(0);

    // Verify that tooltip is present (in collapsed mode, items show tooltips)
    const tooltip = targetMenuItem.locator('cctc-tooltip');
    await expect(tooltip).toHaveCount(1);

    // Verify the tooltip has the correct title in the data attribute
    await expect(tooltip).toHaveAttribute('data-bs-original-title', itemTitle);
  }
);
