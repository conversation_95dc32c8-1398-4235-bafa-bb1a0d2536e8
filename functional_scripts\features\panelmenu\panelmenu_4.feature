@component @panelmenu @panelmenu_4
Feature: the headers can be collapsed
    Scenario: the header is collapsed
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        And the user expands the concertina by clicking on the header with the text "User collapsible menu"
        And the panel menu expand and collapse arrows point "left"
        And the panel menu item 1 has no tooltip enabled
        And the panel menu matches the base image "expanded panel menu"
        When the user clicks the panel menu expand collapse bar
        Then the panel menu expand and collapse arrows point "right"
        And the panel menu item 1 has a tooltip enabled containing the full label text
        And the panel menu matches the base image "collapsed panel menu"

# the problem with the tooltips is that they do exist but they do not seem to match what they are expecting

# i can easily identify that there ARE tooltips or not, but not that they match the inner text, so wonder if it works differently here. 

