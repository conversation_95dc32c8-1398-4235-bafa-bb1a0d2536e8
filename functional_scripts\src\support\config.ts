import { LaunchOptions } from '@playwright/test';
const browserOptions: LaunchOptions = {
  slowMo: 0,
  headless: true,
  args: [
    '--use-fake-ui-for-media-stream',
    '--use-fake-device-for-media-stream',
    '--force-device-scale-factor=1.0'
  ],
  firefoxUserPrefs: {
    'media.navigator.streams.fake': true,
    'media.navigator.permission.disabled': true
  }
};

// set the base config options
// note the use of the preview website
export const config = {
  browser: process.env.BROWSER ?? 'chrome',
  browserOptions,
  //BASE_URL: 'https://gray-tree-00d961c03-preview.5.azurestaticapps.net',
  // BASE_URL: 'https://calm-pond-09746c803-preview.westeurope.5.azurestaticapps.net',
  BASE_URL: 'https://localhost:7021',
  IMG_THRESHOLD: { threshold: 0.4 }, // ranges from 0 to 1. Smaller values make the comparison more sensitive.
  SCREENSHOT_MAX_RETRIES: 10,
  BASE_API_URL: 'https://catfact.ninja/',
  TESTID_ATTRIBUTE: 'data-pw',
  DEFAULT_TIMEOUT: process.env.PWDEBUG ? -1 : 60 * 1000 // Infinite in debug mode or 60 seconds
};
