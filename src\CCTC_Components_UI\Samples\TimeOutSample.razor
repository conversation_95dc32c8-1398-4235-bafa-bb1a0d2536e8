﻿@page "/timeoutsample"

@{
    var description = new List<string>
    {
        "The TimeOut component is a simple component that shows content on screen for a predefined period of time before fading out."
    };

    var features = new List<(string, string)>
    {
        ("Initial display", "The content can reserve space or initialise in a collapsed state"),
        ("Display on completion", "The content can either collapse or reserve its consumed space on completion, irrespective of its " +
                                  "intialisation state"),
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Reserved and no collapse", "Space is reserved and does not collapse on completion",
            @"<TimeOut
    Id=""sample1""
    @ref=""@_sample1""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""true""
    CollapseOnFadeComplete=""false"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample1;

    void RunSample1()
    {
        _sample1?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample1"
                    @ref="@_sample1"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="true"
                    CollapseOnFadeComplete="false">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample1">Initiate</div>
            </div>),

        ("Reserved and collapses", "Space is reserved initially, but then collapses on completion",
            @"<TimeOut
    Id=""sample2""
    @ref=""@_sample2""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""true""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample2;

    void RunSample2()
    {
        _sample2?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample2"
                    @ref="@_sample2"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="true"
                    CollapseOnFadeComplete="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample2">Initiate</div>
            </div>),

        ("Not reserved and collapses", "Space is not reserved initially and collapses on completion",
            @"<TimeOut
    Id=""sample3""
    @ref=""@_sample3""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""false""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample3;

    void RunSample3()
    {
        _sample3?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample3"
                    @ref="@_sample3"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample3">Initiate</div>
            </div>),

        ("Not reserved and does not collapse", "Space is not reserved initially and space stays reserved on completion",
            @"<TimeOut
    Id=""sample4""
    @ref=""@_sample4""
    ShowContentFor=""TimeSpan.FromSeconds(1)""
    FadeFor=""TimeSpan.FromSeconds(2)""
    AfterShowContent=""@(() => Console.WriteLine(""Show content finished""))""
    AfterFadeFinished=""@(() => Console.WriteLine(""Fade finished""))""
    ReserveSpace=""false""
    CollapseOnFadeComplete=""true"">
    <div class=""sample-content"">
        <div class=""d-flex flex-column"">
            <div class=""material-icons"" style=""font-size: 24px"">thumb_up</div>
            <div>Some content to display in the message</div>
        </div>
    </div>
</TimeOut>

@code {

    TimeOut? _sample4;

    void RunSample4()
    {
        _sample4?.Initiate();
    }
}",
            @<div>
                <hr/>
                <TimeOut
                    Id="sample4"
                    @ref="@_sample4"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="false">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>
                <hr/>
                <div class="initiate" @onclick="RunSample4">Initiate</div>
            </div>)
    };
}

<div required-so-deep-works>
    <Sampler
        ComponentName="TimeOut"
        ComponentCssName="timeout"
        Description="@description"
        UsageText="Typical usages of the <code>TimeOut</code> component are shown below"
        UsageCodeList="@usageCode"
        ContentHeightPixels="400"
        Features="@features">
        <ExampleTemplate>

            <div class="m-1">
                <div>The timeout is located between the horizontal lines.</div>
                <div>The space is collapsed until the timer is initiated, and once elapsed, the space is collapsed again.</div>
                <hr/>

                <TimeOut
                    Id="example"
                    @ref="@_example"
                    ShowContentFor="TimeSpan.FromSeconds(1)"
                    FadeFor="TimeSpan.FromSeconds(2)"
                    AfterShowContent="@(() => Console.WriteLine("Show content finished"))"
                    AfterFadeFinished="@(() => Console.WriteLine("Fade finished"))"
                    ReserveSpace="false"
                    CollapseOnFadeComplete="true">
                    <div class="sample-content">
                        <div class="d-flex flex-column">
                            <div class="material-icons" style="font-size: 24px">thumb_up</div>
                            <div>Some content to display in the message</div>
                        </div>
                    </div>
                </TimeOut>

                <hr/>

                <div class="initiate" @onclick="RunExample">Initiate</div>

            </div>

        </ExampleTemplate>
    </Sampler>
</div>

@code {

    TimeOut? _sample1;

    void RunSample1()
    {
        _sample1?.Initiate();
    }

    TimeOut? _sample2;

    void RunSample2()
    {
        _sample2?.Initiate();
    }

    TimeOut? _sample3;

    void RunSample3()
    {
        _sample3?.Initiate();
    }

    TimeOut? _sample4;

    void RunSample4()
    {
        _sample4?.Initiate();
    }

    TimeOut? _example;

    void RunExample()
    {
        _example?.Initiate();
    }
}