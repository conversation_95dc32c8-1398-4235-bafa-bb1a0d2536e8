@component @panelmenu @panelmenu_6
Feature: text associated with an item can be hidden
    Scenario: an item can have no visible text
        Given the user is at the home page
        And the user selects the "Panel menu" component in the container "Panel menu"
        And the user clicks the "Usage" tab
        When the user expands the concertina by clicking on the header with the text "Typical usage (icons only)"
        Then the panel menu header "menuitem without header" shows only the icon and not the text
        And the panel menu matches the base image "panel menu icons only"
