﻿@page "/panelmenusample"

@{
    var description = new List<string>
    {
        "The PanelMenu component provides a panel of navigation options. " +
        "A PanelMenu contains PanelMenuHeaders and PanelMenuItems. PanelMenuItems can be grouped under a PanelMenuHeader, " +
        "or can be direct descendants of the PanelMenu. Each item can be displayed with or without icons, and with or without " +
        "descriptive text."
    };

    var features = new List<(string, string)>
    {
        ("icons only", "setting <code>IconsOnly</code> will hide text associated with an item"),
        ("single or multi expand", "setting <code>PanelMenuExpand</code> to multiple will allow more than one menu header to be expanded"),
        ("user collapse", "setting <code>CanUserCollapse</code> to true will show the collapse icon and allow the user to collapse and expand the menu as a whole"),
    };

    var gotchas = new List<(string, string)>
    {
        ("width", "by default the panel menu will take all available space")
    };

    var usage = new List<string>
    {
    };

    var usageCode = new List<(string title, string description, string code, RenderFragment fragment)>
    {
        ("Typical usage", "an example with panel items as direct descendants of the panel menu, as well as collected under a panel menu header",
            @"<PanelMenu Id=""panel-menu"" MatchPathPred=""@PanelMenuMatches.Default"" PanelMenuExpand=""PanelMenuExpand.Single"">
    <PanelMenuItem Id=""MenuItem1"" Title=""menuitem without header"" Icon=""edit"" Path=""panelmenusample?item=MenuItem1""></PanelMenuItem>
    <PanelMenuItem Id=""MenuItem2"" Title=""menuitem without header 2 really quite long here"" Icon=""edit"" Path=""panelmenusample?item=MenuItem2""></PanelMenuItem>
    <PanelMenuHeader Id=""1"" Title=""header 1"" Icon=""circle"">
        <PanelMenuItem Id=""1A"" Title=""panel item 1A"" Icon=""edit"" Path=""panelmenusample?item=1A""
                       PanelMenuItemSelected=@(name => Console.WriteLine($""{name} clicked""))>
        </PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""2"" Title=""header 2"" Icon=""circle"">
        <PanelMenuItem Id=""2A"" Title=""panel item 2A"" Icon=""edit"" Path=""panelmenusample?item=2A""></PanelMenuItem>
        <PanelMenuItem Id=""2B"" Title=""panel item 2B"" Icon=""edit"" Path=""panelmenusample?item=2B""></PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""3"" Title=""header 3"" Icon=""circle"">
        <PanelMenuItem Id=""3A"" Title=""panel item 3A"" Icon=""edit"" Path=""panelmenusample?item=3A""></PanelMenuItem>
        <PanelMenuItem Id=""3B"" Title=""panel item 3B"" Icon=""edit"" Path=""panelmenusample?item=3B""></PanelMenuItem>
    </PanelMenuHeader>
</PanelMenu>", @<PanelMenu Id="panel-menu" MatchPathPred="@PanelMenuMatches.Default" PanelMenuExpand="PanelMenuExpand.Single">
        <PanelMenuItem Id="MenuItem1" Title="menuitem without header" Icon="edit" Path="panelmenusample?item=MenuItem1"></PanelMenuItem>
        <PanelMenuItem Id="MenuItem2" Title="menuitem without header 2 really quite long here" Icon="edit" Path="panelmenusample?item=MenuItem2"></PanelMenuItem>
        <PanelMenuHeader Id="1" Title="header 1" Icon="circle">
            <PanelMenuItem Id="1A" Title="panel item 1A" Icon="edit" Path="panelmenusample?item=1A"
                           PanelMenuItemSelected=@(id => Console.WriteLine($"{id} clicked"))>
            </PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuHeader Id="2" Title="header 2" Icon="circle">
            <PanelMenuItem Id="2A" Title="panel item 2A" Icon="edit" Path="panelmenusample?item=2A"></PanelMenuItem>
            <PanelMenuItem Id="2B" Title="panel item 2B" Icon="edit" Path="panelmenusample?item=2B"></PanelMenuItem>
        </PanelMenuHeader>
        <PanelMenuHeader Id="3" Title="header 3" Icon="circle">
            <PanelMenuItem Id="3A" Title="panel item 3A" Icon="edit" Path="panelmenusample?item=3A"></PanelMenuItem>
            <PanelMenuItem Id="3B" Title="panel item 3B" Icon="edit" Path="panelmenusample?item=3B"></PanelMenuItem>
        </PanelMenuHeader>
    </PanelMenu>
    ),

    ("User collapsible menu", "The menu can be collapsed or expanded by the user via an icon in the top left. This is enabled by setting <code>CanUserCollapse</code> to <code>true</code>.",
        @"<PanelMenu Id=""panel-menu"" MatchPathPred=""@PanelMenuMatches.Default"" PanelMenuExpand=""PanelMenuExpand.Single"" CanUserCollapse=""true"">
    <PanelMenuItem Id=""MenuItem1"" Title=""menuitem without header"" Icon=""edit"" Path=""panelmenusample?item=MenuItem1""></PanelMenuItem>
    <PanelMenuItem Id=""MenuItem2"" Title=""menuitem without header 2 really quite long here"" Icon=""edit"" Path=""panelmenusample?item=MenuItem2""></PanelMenuItem>
    <PanelMenuHeader Id=""1"" Title=""header 1"" Icon=""circle"">
        <PanelMenuItem Id=""1A"" Title=""panel item 1A"" Icon=""edit"" Path=""panelmenusample?item=1A""
                       PanelMenuItemSelected=@(name => Console.WriteLine($""{name} clicked""))>
        </PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""2"" Title=""header 2"" Icon=""circle"">
        <PanelMenuItem Id=""2A"" Title=""panel item 2A"" Icon=""edit"" Path=""panelmenusample?item=2A""></PanelMenuItem>
        <PanelMenuItem Id=""2B"" Title=""panel item 2B"" Icon=""edit"" Path=""panelmenusample?item=2B""></PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""3"" Title=""header 3"" Icon=""circle"">
        <PanelMenuItem Id=""3A"" Title=""panel item 3A"" Icon=""edit"" Path=""panelmenusample?item=3A""></PanelMenuItem>
        <PanelMenuItem Id=""3B"" Title=""panel item 3B"" Icon=""edit"" Path=""panelmenusample?item=3B""></PanelMenuItem>
    </PanelMenuHeader>
</PanelMenu>",
        @<PanelMenu Id="panel-menu" MatchPathPred="@PanelMenuMatches.Default" PanelMenuExpand="PanelMenuExpand.Single" CanUserCollapse="true">
            <PanelMenuItem Id="MenuItem1" Title="menuitem without header" Icon="edit" Path="panelmenusample?item=MenuItem1"></PanelMenuItem>
            <PanelMenuItem Id="MenuItem2" Title="menuitem without header 2 really quite long here" Icon="edit" Path="panelmenusample?item=MenuItem2"></PanelMenuItem>
            <PanelMenuHeader Id="1" Title="header 1" Icon="circle">
                <PanelMenuItem Id="1A" Title="panel item 1A" Icon="edit" Path="panelmenusample?item=1A"
                               PanelMenuItemSelected=@(id => Console.WriteLine($"{id} clicked"))>
                </PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="2" Title="header 2" Icon="circle">
                <PanelMenuItem Id="2A" Title="panel item 2A" Icon="edit" Path="panelmenusample?item=2A"></PanelMenuItem>
                <PanelMenuItem Id="2B" Title="panel item 2B" Icon="edit" Path="panelmenusample?item=2B"></PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="3" Title="header 3" Icon="circle">
                <PanelMenuItem Id="3A" Title="panel item 3A" Icon="edit" Path="panelmenusample?item=3A"></PanelMenuItem>
                <PanelMenuItem Id="3B" Title="panel item 3B" Icon="edit" Path="panelmenusample?item=3B"></PanelMenuItem>
            </PanelMenuHeader>
        </PanelMenu>
    ),

    @* ("Multi-expand", "Multiple menu headers can be expanded at once by setting <code>PanelMenuExpand</code> to <code>PanelMenuExpand.Multiple</code>.",
        @"<PanelMenu Id=""panel-menu"" MatchPathPred=""@PanelMenuMatches.Default"" PanelMenuExpand=""PanelMenuExpand.Multiple"">
    <PanelMenuItem Id=""MenuItem1"" Title=""menuitem without header"" Icon=""edit"" Path=""panelmenusample?item=MenuItem1""></PanelMenuItem>
    <PanelMenuItem Id=""MenuItem2"" Title=""menuitem without header 2 really quite long here"" Icon=""edit"" Path=""panelmenusample?item=MenuItem2""></PanelMenuItem>
    <PanelMenuHeader Id=""1"" Title=""header 1"" Icon=""circle"">
        <PanelMenuItem Id=""1A"" Title=""panel item 1A"" Icon=""edit"" Path=""panelmenusample?item=1A""
                       PanelMenuItemSelected=@(name => Console.WriteLine($""{name} clicked""))>
        </PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""2"" Title=""header 2"" Icon=""circle"">
        <PanelMenuItem Id=""2A"" Title=""panel item 2A"" Icon=""edit"" Path=""panelmenusample?item=2A""></PanelMenuItem>
        <PanelMenuItem Id=""2B"" Title=""panel item 2B"" Icon=""edit"" Path=""panelmenusample?item=2B""></PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""3"" Title=""header 3"" Icon=""circle"">
        <PanelMenuItem Id=""3A"" Title=""panel item 3A"" Icon=""edit"" Path=""panelmenusample?item=3A""></PanelMenuItem>
        <PanelMenuItem Id=""3B"" Title=""panel item 3B"" Icon=""edit"" Path=""panelmenusample?item=3B""></PanelMenuItem>
    </PanelMenuHeader>
</PanelMenu>",
        @<PanelMenu Id="panel-menu" MatchPathPred="@PanelMenuMatches.Default" PanelMenuExpand="PanelMenuExpand.Multiple">
            <PanelMenuItem Id="MenuItem1" Title="menuitem without header" Icon="edit" Path="panelmenusample?item=MenuItem1"></PanelMenuItem>
            <PanelMenuItem Id="MenuItem2" Title="menuitem without header 2 really quite long here" Icon="edit" Path="panelmenusample?item=MenuItem2"></PanelMenuItem>
            <PanelMenuHeader Id="1" Title="header 1" Icon="circle">
                <PanelMenuItem Id="1A" Title="panel item 1A" Icon="edit" Path="panelmenusample?item=1A"
                               PanelMenuItemSelected=@(id => Console.WriteLine($"{id} clicked"))>
                </PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="2" Title="header 2" Icon="circle">
                <PanelMenuItem Id="2A" Title="panel item 2A" Icon="edit" Path="panelmenusample?item=2A"></PanelMenuItem>
                <PanelMenuItem Id="2B" Title="panel item 2B" Icon="edit" Path="panelmenusample?item=2B"></PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="3" Title="header 3" Icon="circle">
                <PanelMenuItem Id="3A" Title="panel item 3A" Icon="edit" Path="panelmenusample?item=3A"></PanelMenuItem>
                <PanelMenuItem Id="3B" Title="panel item 3B" Icon="edit" Path="panelmenusample?item=3B"></PanelMenuItem>
            </PanelMenuHeader>
        </PanelMenu>
    ), *@

    ("Typical usage (icons only)", "An example showing panel items with icons only (no text) by setting the <code>IconsOnly</code> parameter.",
        @"<PanelMenu Id=""panel-menu"" MatchPathPred=""@PanelMenuMatches.Default"" PanelMenuExpand=""PanelMenuExpand.Single"" IconsOnly=""true"">
    <PanelMenuItem Id=""MenuItem1"" Title=""menuitem without header"" Icon=""edit"" Path=""panelmenusample?item=MenuItem1""></PanelMenuItem>
    <PanelMenuItem Id=""MenuItem2"" Title=""menuitem without header 2 really quite long here"" Icon=""edit"" Path=""panelmenusample?item=MenuItem2""></PanelMenuItem>
    <PanelMenuHeader Id=""1"" Title=""header 1"" Icon=""circle"">
        <PanelMenuItem Id=""1A"" Title=""panel item 1A"" Icon=""edit"" Path=""panelmenusample?item=1A""
                       PanelMenuItemSelected=@(name => Console.WriteLine($""{name} clicked""))>
        </PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""2"" Title=""header 2"" Icon=""circle"">
        <PanelMenuItem Id=""2A"" Title=""panel item 2A"" Icon=""edit"" Path=""panelmenusample?item=2A""></PanelMenuItem>
        <PanelMenuItem Id=""2B"" Title=""panel item 2B"" Icon=""edit"" Path=""panelmenusample?item=2B""></PanelMenuItem>
    </PanelMenuHeader>
    <PanelMenuHeader Id=""3"" Title=""header 3"" Icon=""circle"">
        <PanelMenuItem Id=""3A"" Title=""panel item 3A"" Icon=""edit"" Path=""panelmenusample?item=3A""></PanelMenuItem>
        <PanelMenuItem Id=""3B"" Title=""panel item 3B"" Icon=""edit"" Path=""panelmenusample?item=3B""></PanelMenuItem>
    </PanelMenuHeader>
</PanelMenu>",
        @<PanelMenu Id="panel-menu" MatchPathPred="@PanelMenuMatches.Default" PanelMenuExpand="PanelMenuExpand.Single" IconsOnly="true">
            <PanelMenuItem Id="MenuItem1" Title="menuitem without header" Icon="edit" Path="panelmenusample?item=MenuItem1"></PanelMenuItem>
            <PanelMenuItem Id="MenuItem2" Title="menuitem without header 2 really quite long here" Icon="edit" Path="panelmenusample?item=MenuItem2"></PanelMenuItem>
            <PanelMenuHeader Id="1" Title="header 1" Icon="circle">
                <PanelMenuItem Id="1A" Title="panel item 1A" Icon="edit" Path="panelmenusample?item=1A"
                               PanelMenuItemSelected=@(id => Console.WriteLine($"{id} clicked"))>
                </PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="2" Title="header 2" Icon="circle">
                <PanelMenuItem Id="2A" Title="panel item 2A" Icon="edit" Path="panelmenusample?item=2A"></PanelMenuItem>
                <PanelMenuItem Id="2B" Title="panel item 2B" Icon="edit" Path="panelmenusample?item=2B"></PanelMenuItem>
            </PanelMenuHeader>
            <PanelMenuHeader Id="3" Title="header 3" Icon="circle">
                <PanelMenuItem Id="3A" Title="panel item 3A" Icon="edit" Path="panelmenusample?item=3A"></PanelMenuItem>
                <PanelMenuItem Id="3B" Title="panel item 3B" Icon="edit" Path="panelmenusample?item=3B"></PanelMenuItem>
            </PanelMenuHeader>
        </PanelMenu>
    )
};

    var relatedComponents = new List<(Relation relation, string display, string url, string description)>
    {
        (Relation.Child, "PanelMenuHeader", "panelmenuheadersample", "A PanelMenu can contain PanelMenuHeaders that allow grouping of PanelMenuItems"),
        (Relation.Child, "PanelMenuItem", "panelmenuitemsample", "A PanelMenu can contain PanelMenuItems that that are used for navigating"),
    };
}

<Sampler
    ComponentName="PanelMenu"
    ComponentCssName="panelmenu"
    Description="@description"
    RelatedComponents="@relatedComponents"
    UsageText="Typical usages of the <code>PanelMenu</code> component are shown below"
    UsageCodeList="@usageCode"
    ContentHeightPixels="400"
    Features="@features"
    Gotchas="@gotchas"
    >
    <ExampleTemplate>
        <div>
            <PanelMenu Id="panel-menu-example" MatchPathPred="@PanelMenuMatches.Default" PanelMenuExpand="PanelMenuExpand.Single">
                <PanelMenuItem Id="panel-menu-example-MenuItem1" Title="menuitem without header" Icon="edit" Path="panelmenusample?item=MenuItem1"></PanelMenuItem>
                <PanelMenuItem Id="panel-menu-example-MenuItem2" Title="menuitem without header 2 really quite long here" Icon="edit" Path="panelmenusample?item=MenuItem2"></PanelMenuItem>
                <PanelMenuHeader Id="panel-menu-example-1" Title="header 1" Icon="circle">
                    <PanelMenuItem Id="panel-menu-example-1A" Title="panel item 1A" Icon="edit" Path="panelmenusample?item=1A"
                                   PanelMenuItemSelected=@(id => Console.WriteLine($"{id} clicked"))>
                    </PanelMenuItem>
                </PanelMenuHeader>
                <PanelMenuHeader Id="panel-menu-example-2" Title="header 2" Icon="circle">
                    <PanelMenuItem Id="panel-menu-example-2A" Title="panel item 2A" Icon="edit" Path="panelmenusample?item=2A"></PanelMenuItem>
                    <PanelMenuItem Id="panel-menu-example-2B" Title="panel item 2B" Icon="edit" Path="panelmenusample?item=2B"></PanelMenuItem>
                </PanelMenuHeader>
                <PanelMenuHeader Id="panel-menu-example-3" Title="header 3" Icon="circle">
                    <PanelMenuItem Id="panel-menu-example-3A" Title="panel item 3A" Icon="edit" Path="panelmenusample?item=3A"></PanelMenuItem>
                    <PanelMenuItem Id="panel-menu-example-3B" Title="panel item 3B" Icon="edit" Path="panelmenusample?item=3B"></PanelMenuItem>
                </PanelMenuHeader>
            </PanelMenu>

            <div class="mt-3">
                selected: @(Item ?? "nothing yet")
            </div>
        </div>
    </ExampleTemplate>
</Sampler>

@code {

    [Parameter]
    [SupplyParameterFromQuery(Name = "item")]
    public string? Item { get; set; }

}