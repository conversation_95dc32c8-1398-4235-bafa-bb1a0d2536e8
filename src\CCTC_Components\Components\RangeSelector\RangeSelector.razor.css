.CCTC-range-selector-container {
    width: 50%;
    margin: 20px 0;
    user-select: none;
}

.CCTC-range-selector-track {
    position: relative;
    height: 10px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin: 20px 0;
}

.CCTC-range-selector-fill {
    position: absolute;
    height: 100%;
    background-color: #007bff;
    border-radius: 3px;
    transition: none;
}

.CCTC-range-selector-thumb {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #007bff;
    border: 2px solid white;
    border-radius: 50%;
    top: -7px;
    margin-left: -10px;
    cursor: grab;
    transition: box-shadow 0.2s ease, transform 0.2s ease;
    z-index: 2;
}

.CCTC-range-selector-thumb:active,
.CCTC-range-selector-thumb.CCTC-dragging {
    cursor: grabbing;
}

.CCTC-range-selector-thumb:hover {
    box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.2);
    transform: scale(1.1);
}

.CCTC-range-selector-thumb.CCTC-dragging {
    box-shadow: 0 0 0 12px rgba(0, 123, 255, 0.3);
    transform: scale(1.2);
    z-index: 10;
}

.CCTC-range-selector-thumb-lower {
    z-index: 3;
}

.CCTC-range-selector-thumb-upper {
    z-index: 3;
}

.CCTC-range-selector-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.CCTC-range-selector-thumb:focus {
    outline: none;
    box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.3);
}