@using Microsoft.AspNetCore.Components
@using Microsoft.JSInterop
@implements IAsyncDisposable
@inject IJSRuntime JSRuntime
@typeparam TValue where TValue : IComparable<TValue>

<div class="CCTC-range-selector-container" id="@Id">
    <div class="CCTC-range-selector-track" @ref="trackElement">
        <div class="CCTC-range-selector-fill" style="left: @(LowerPercentage)%; width: @(UpperPercentage - LowerPercentage)%;"></div>
        <div class="CCTC-range-selector-thumb CCTC-range-selector-thumb-lower" 
             style="left: @(LowerPercentage)%;" 
             tabindex="0"
             aria-label="Lower range handle"
             role="slider"
             aria-valuemin="@GetDisplayValue(InitialOuterRangeLower)"
             aria-valuemax="@GetDisplayValue(InitialOuterRangeUpper)"
             aria-valuenow="@GetDisplayValue(_currentLower)"
             @onkeydown="@(async (e) => await OnKeyDownAsync(e, true))"
             @onmousedown="@(async (e) => await OnMouseDownAsync(e, true))"
             @ontouchstart="@(async (e) => await OnTouchStartAsync(e, true))"
             @ontouchstart:preventDefault="true"></div>
        <div class="CCTC-range-selector-thumb CCTC-range-selector-thumb-upper" 
             style="left: @(UpperPercentage)%;" 
             tabindex="0"
             aria-label="Upper range handle"
             role="slider"
             aria-valuemin="@GetDisplayValue(InitialOuterRangeLower)"
             aria-valuemax="@GetDisplayValue(InitialOuterRangeUpper)"
             aria-valuenow="@GetDisplayValue(_currentUpper)"
             @onkeydown="@(async (e) => await OnKeyDownAsync(e, false))"
             @onmousedown="@(async (e) => await OnMouseDownAsync(e, false))"
             @ontouchstart="@(async (e) => await OnTouchStartAsync(e, false))"
             @ontouchstart:preventDefault="true"></div>
    </div>
    <div class="CCTC-range-selector-labels">
        <span>@GetDisplayValue(InitialOuterRangeLower)</span>
        <span>@GetDisplayValue(InitialOuterRangeUpper)</span>
    </div>
    @ChildContent
</div>

@code {
    [Parameter] public string Id { get; set; } = Guid.NewGuid().ToString();
    [Parameter] public TValue InitialInnerRangeLower { get; set; } = default!;
    [Parameter] public TValue InitialInnerRangeUpper { get; set; } = default!;
    [Parameter] public TValue InitialOuterRangeLower { get; set; } = default!;
    [Parameter] public TValue InitialOuterRangeUpper { get; set; } = default!;
    [Parameter] public EventCallback<TValue> ActualInnerRangeLower { get; set; }
    [Parameter] public EventCallback<TValue> ActualInnerRangeUpper { get; set; }
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public Func<TValue, TValue, double, TValue>? ValueFromPercentage { get; set; }
    [Parameter] public Func<TValue, TValue, TValue, double>? PercentageFromValue { get; set; }
    [Parameter] public Func<TValue, TValue, int>? StepFunction { get; set; }
    [Parameter] public Func<TValue, string>? DisplayFormatter { get; set; }

    private ElementReference trackElement;
    private IJSObjectReference? jsModule;
    private DotNetObjectReference<RangeSelector<TValue>>? dotNetRef;
    
    private TValue _currentLower = default!;
    private TValue _currentUpper = default!;
    private bool _isDragging;
    private bool _dragIsLowerThumb;

    private double LowerPercentage => GetPercentageFromValue(_currentLower);
    private double UpperPercentage => GetPercentageFromValue(_currentUpper);

    protected override void OnInitialized()
    {
        _currentLower = InitialInnerRangeLower;
        _currentUpper = InitialInnerRangeUpper;
        
        // Validate that we have the required conversion functions for non-numeric types
        if (typeof(TValue) != typeof(int) && typeof(TValue) != typeof(double) && typeof(TValue) != typeof(float) && typeof(TValue) != typeof(decimal))
        {
            if (ValueFromPercentage == null || PercentageFromValue == null)
            {
                throw new InvalidOperationException($"ValueFromPercentage and PercentageFromValue parameters are required for type {typeof(TValue).Name}");
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/rangeSlider.js");
                dotNetRef = DotNetObjectReference.Create(this);
                await jsModule.InvokeVoidAsync("initialize", trackElement, dotNetRef);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading range slider JS: {ex.Message}");
            }
        }
    }

    private async Task OnKeyDownAsync(KeyboardEventArgs e, bool isLowerThumb)
    {
        var handled = false;
        var stepSize = GetStepSize();

        switch (e.Key)
        {
            case "ArrowLeft":
            case "ArrowDown":
                await MoveThumbByStepAsync(isLowerThumb, -stepSize);
                handled = true;
                break;
            case "ArrowRight":
            case "ArrowUp":
                await MoveThumbByStepAsync(isLowerThumb, stepSize);
                handled = true;
                break;
            case "Home":
                await MoveThumbToPositionAsync(isLowerThumb, 0);
                handled = true;
                break;
            case "End":
                await MoveThumbToPositionAsync(isLowerThumb, 100);
                handled = true;
                break;
        }

        if (handled && jsModule != null)
        {
            await jsModule.InvokeVoidAsync("preventDefault", e);
        }
    }

    private async Task OnMouseDownAsync(MouseEventArgs e, bool isLowerThumb)
    {
        _isDragging = true;
        _dragIsLowerThumb = isLowerThumb;
        
        if (jsModule != null)
        {
            await jsModule.InvokeVoidAsync("startDrag", isLowerThumb);
        }
    }

    private async Task OnTouchStartAsync(TouchEventArgs e, bool isLowerThumb)
    {
        _isDragging = true;
        _dragIsLowerThumb = isLowerThumb;
        
        if (jsModule != null)
        {
            await jsModule.InvokeVoidAsync("startDrag", isLowerThumb);
        }
    }

    private async Task MoveThumbByStepAsync(bool isLowerThumb, int step)
    {
        var currentPercentage = isLowerThumb ? LowerPercentage : UpperPercentage;
        var newPercentage = Math.Max(0, Math.Min(100, currentPercentage + step));
        await UpdateValueAsync(newPercentage, isLowerThumb);
    }

    private async Task MoveThumbToPositionAsync(bool isLowerThumb, double percentage)
    {
        await UpdateValueAsync(percentage, isLowerThumb);
    }

    [JSInvokable]
    public async Task UpdateValueAsync(double percentage, bool isLowerThumb)
    {
        var newValue = GetValueFromPercentage(percentage);
        
        if (isLowerThumb)
        {
            if (newValue.CompareTo(_currentUpper) <= 0 && newValue.CompareTo(InitialOuterRangeLower) >= 0)
            {
                _currentLower = newValue;
                await ActualInnerRangeLower.InvokeAsync(_currentLower);
            }
        }
        else
        {
            if (newValue.CompareTo(_currentLower) >= 0 && newValue.CompareTo(InitialOuterRangeUpper) <= 0)
            {
                _currentUpper = newValue;
                await ActualInnerRangeUpper.InvokeAsync(_currentUpper);
            }
        }
        
        await InvokeAsync(StateHasChanged);
    }

    [JSInvokable]
    public async Task StopDragAsync()
    {
        _isDragging = false;
        await InvokeAsync(StateHasChanged);
    }

    private TValue GetValueFromPercentage(double percentage)
    {
        if (ValueFromPercentage != null)
        {
            return ValueFromPercentage(InitialOuterRangeLower, InitialOuterRangeUpper, percentage);
        }

        // Default implementation for numeric types
        if (typeof(TValue) == typeof(int))
        {
            var lower = Convert.ToDouble(InitialOuterRangeLower);
            var upper = Convert.ToDouble(InitialOuterRangeUpper);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)(int)Math.Round(value);
        }
        
        if (typeof(TValue) == typeof(double))
        {
            var lower = Convert.ToDouble(InitialOuterRangeLower);
            var upper = Convert.ToDouble(InitialOuterRangeUpper);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)value;
        }
        
        if (typeof(TValue) == typeof(float))
        {
            var lower = Convert.ToDouble(InitialOuterRangeLower);
            var upper = Convert.ToDouble(InitialOuterRangeUpper);
            var value = lower + (percentage / 100.0) * (upper - lower);
            return (TValue)(object)(float)value;
        }
        
        if (typeof(TValue) == typeof(decimal))
        {
            var lower = Convert.ToDecimal(InitialOuterRangeLower);
            var upper = Convert.ToDecimal(InitialOuterRangeUpper);
            var value = lower + (decimal)(percentage / 100.0) * (upper - lower);
            return (TValue)(object)value;
        }

        throw new NotSupportedException($"Type {typeof(TValue).Name} is not supported without custom conversion functions.");
    }

    private double GetPercentageFromValue(TValue value)
    {
        if (PercentageFromValue != null)
        {
            return PercentageFromValue(InitialOuterRangeLower, InitialOuterRangeUpper, value);
        }

        // Default implementation for numeric types
        if (typeof(TValue) == typeof(int) || typeof(TValue) == typeof(double) || 
            typeof(TValue) == typeof(float) || typeof(TValue) == typeof(decimal))
        {
            var lower = Convert.ToDouble(InitialOuterRangeLower);
            var upper = Convert.ToDouble(InitialOuterRangeUpper);
            var current = Convert.ToDouble(value);
            
            if (upper == lower) return 0;
            return ((current - lower) / (upper - lower)) * 100.0;
        }

        throw new NotSupportedException($"Type {typeof(TValue).Name} is not supported without custom conversion functions.");
    }

    private int GetStepSize()
    {
        if (StepFunction != null)
        {
            return StepFunction(InitialOuterRangeLower, InitialOuterRangeUpper);
        }

        // Default step size based on range
        var range = GetPercentageFromValue(InitialOuterRangeUpper) - GetPercentageFromValue(InitialOuterRangeLower);
        return Math.Max(1, (int)(range / 100)); // 1% of range, minimum 1
    }

    private string GetDisplayValue(TValue value)
    {
        if (DisplayFormatter != null)
        {
            return DisplayFormatter(value);
        }

        return value?.ToString() ?? string.Empty;
    }

    public async ValueTask DisposeAsync()
    {
        dotNetRef?.Dispose();
        if (jsModule != null)
        {
            await jsModule.DisposeAsync();
        }
    }
}